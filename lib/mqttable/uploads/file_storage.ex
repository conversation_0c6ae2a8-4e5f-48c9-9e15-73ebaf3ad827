defmodule Mqttable.Uploads.FileStorage do
  @moduledoc """
  Module for handling file storage for MQTT message payloads.
  
  Files are stored in priv/data/files directory with unique filenames
  to avoid conflicts and enable proper file management.
  """

  require Logger

  @files_dir "priv/data/files"
  @max_file_size 16 * 1024 * 1024  # 16MB

  @doc """
  Returns the path to the files directory.
  """
  def files_dir, do: @files_dir

  @doc """
  Returns the maximum allowed file size in bytes.
  """
  def max_file_size, do: @max_file_size

  @doc """
  Ensures the files directory exists.
  """
  def ensure_files_dir do
    files_dir = Path.join([:code.priv_dir(:mqttable), "data", "files"])
    File.mkdir_p!(files_dir)
    files_dir
  end

  @doc """
  Stores a file with a unique filename and returns the relative file path.
  
  ## Parameters
  - `file_content`: Binary content of the file
  - `original_filename`: Original filename (used for extension)
  
  ## Returns
  - `{:ok, file_path}`: On success, returns the relative file path
  - `{:error, reason}`: On failure, returns the error reason
  """
  def store_file(file_content, original_filename) when is_binary(file_content) do
    if byte_size(file_content) > @max_file_size do
      {:error, "File too large. Maximum size is #{format_file_size(@max_file_size)}"}
    else
      try do
        # Ensure directory exists
        ensure_files_dir()
        
        # Generate unique filename
        timestamp = System.system_time(:microsecond)
        extension = Path.extname(original_filename)
        base_name = Path.basename(original_filename, extension)
        unique_filename = "#{base_name}_#{timestamp}#{extension}"
        
        # Full path for writing
        full_path = Path.join(@files_dir, unique_filename)
        
        # Write file
        case File.write(full_path, file_content) do
          :ok ->
            Logger.info("File stored successfully: #{unique_filename}")
            {:ok, unique_filename}
          
          {:error, reason} ->
            Logger.error("Failed to write file #{unique_filename}: #{inspect(reason)}")
            {:error, "Failed to save file: #{reason}"}
        end
      rescue
        error ->
          Logger.error("Error storing file: #{inspect(error)}")
          {:error, "Failed to store file"}
      end
    end
  end

  @doc """
  Reads file content from the stored file path.
  
  ## Parameters
  - `file_path`: Relative file path (filename only)
  
  ## Returns
  - `{:ok, content}`: On success, returns the file content
  - `{:error, reason}`: On failure, returns the error reason
  """
  def read_file(file_path) when is_binary(file_path) do
    full_path = Path.join(@files_dir, file_path)
    
    case File.read(full_path) do
      {:ok, content} ->
        {:ok, content}
      
      {:error, :enoent} ->
        {:error, "File not found: #{file_path}"}
      
      {:error, reason} ->
        Logger.error("Failed to read file #{file_path}: #{inspect(reason)}")
        {:error, "Failed to read file: #{reason}"}
    end
  end

  @doc """
  Deletes a stored file.
  
  ## Parameters
  - `file_path`: Relative file path (filename only)
  
  ## Returns
  - `:ok`: On success
  - `{:error, reason}`: On failure
  """
  def delete_file(file_path) when is_binary(file_path) do
    full_path = Path.join(@files_dir, file_path)
    
    case File.rm(full_path) do
      :ok ->
        Logger.info("File deleted successfully: #{file_path}")
        :ok
      
      {:error, :enoent} ->
        # File doesn't exist, consider it successful
        :ok
      
      {:error, reason} ->
        Logger.error("Failed to delete file #{file_path}: #{inspect(reason)}")
        {:error, "Failed to delete file: #{reason}"}
    end
  end

  @doc """
  Lists all stored files with their information.
  
  ## Returns
  - `{:ok, files}`: List of file info maps
  - `{:error, reason}`: On failure
  """
  def list_files do
    case File.ls(@files_dir) do
      {:ok, filenames} ->
        files = 
          filenames
          |> Enum.map(fn filename ->
            full_path = Path.join(@files_dir, filename)
            case File.stat(full_path) do
              {:ok, %{size: size, mtime: mtime}} ->
                %{
                  filename: filename,
                  size: size,
                  modified_at: mtime,
                  size_formatted: format_file_size(size)
                }
              
              {:error, _} ->
                nil
            end
          end)
          |> Enum.reject(&is_nil/1)
        
        {:ok, files}
      
      {:error, reason} ->
        {:error, "Failed to list files: #{reason}"}
    end
  end

  @doc """
  Checks if a file exists.
  
  ## Parameters
  - `file_path`: Relative file path (filename only)
  
  ## Returns
  - `true` if file exists
  - `false` if file doesn't exist
  """
  def file_exists?(file_path) when is_binary(file_path) do
    full_path = Path.join(@files_dir, file_path)
    File.exists?(full_path)
  end

  @doc """
  Gets file information.
  
  ## Parameters
  - `file_path`: Relative file path (filename only)
  
  ## Returns
  - `{:ok, file_info}`: File information map
  - `{:error, reason}`: On failure
  """
  def get_file_info(file_path) when is_binary(file_path) do
    full_path = Path.join(@files_dir, file_path)
    
    case File.stat(full_path) do
      {:ok, %{size: size, mtime: mtime}} ->
        {:ok, %{
          filename: file_path,
          size: size,
          modified_at: mtime,
          size_formatted: format_file_size(size)
        }}
      
      {:error, :enoent} ->
        {:error, "File not found: #{file_path}"}
      
      {:error, reason} ->
        {:error, "Failed to get file info: #{reason}"}
    end
  end

  # Helper function to format file size
  defp format_file_size(bytes) when is_integer(bytes) do
    cond do
      bytes >= 1_073_741_824 -> "#{Float.round(bytes / 1_073_741_824, 2)} GB"
      bytes >= 1_048_576 -> "#{Float.round(bytes / 1_048_576, 2)} MB"
      bytes >= 1024 -> "#{Float.round(bytes / 1024, 2)} KB"
      true -> "#{bytes} Bytes"
    end
  end
end
