defmodule MqttableWeb.ScheduledMessageModalComponent do
  @moduledoc """
  A reusable modal component for creating and editing scheduled MQTT messages.

  This component provides a form for composing scheduled MQTT messages with support for:
  - Pre-filled and disabled client ID selection
  - Send interval configuration in seconds
  - All standard MQTT message properties (topic, payload, QoS, retain)
  - MQTT 5.0 properties support
  - Edit mode for existing scheduled messages
  """

  use MqttableWeb, :live_component
  import MqttableWeb.Shared.MessageFormComponents

  # Template support will be added in future iterations
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:scheduled_message_form, fn -> default_scheduled_message_form() end)
      |> assign_new(:edit_mode, fn -> false end)
      |> assign_new(:edit_index, fn -> nil end)
      |> assign_new(:alert_message, fn -> nil end)
      |> assign_new(:alert_type, fn -> nil end)
      |> assign_new(:uploaded_file, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Handle file upload updates
    cond do
      Map.has_key?(assigns, :file_uploaded) ->
        handle_file_upload_update(assigns, socket)

      Map.has_key?(assigns, :file_upload_error) ->
        handle_file_upload_error_update(assigns, socket)

      true ->
        # Get connected clients for the active broker
        connected_clients = get_connected_clients(assigns[:active_broker_name] || "")

        # Load MQTT 5.0 properties collapse state from ui_state
        mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

        # Initialize or update form based on edit mode and form state
        form =
          if assigns[:edit_mode] && assigns[:scheduled_message] do
            # Edit mode - populate form with existing scheduled message data
            # Normalize the scheduled message to handle both atom and string keys
            scheduled_msg = normalize_scheduled_message(assigns[:scheduled_message])

            # Calculate interval value in seconds from interval_ms
            interval_ms = scheduled_msg.interval_ms || 5000
            interval_seconds = div(interval_ms, 1000)

            %{
              "client_id" => assigns[:pre_selected_client_id] || "",
              "topic" => scheduled_msg.topic || "",
              "payload_format" => scheduled_msg.payload_format || "text",
              "qos" => to_string(scheduled_msg.qos || 0),
              "retain" => scheduled_msg.retain || false,
              "interval_seconds" => to_string(interval_seconds),
              # MQTT 5.0 properties
              "content_type" => scheduled_msg.content_type || "",
              "payload_format_indicator" => scheduled_msg.payload_format_indicator || false,
              "message_expiry_interval" => scheduled_msg.message_expiry_interval || 0,
              "topic_alias" => scheduled_msg.topic_alias || 0,
              "response_topic" => scheduled_msg.response_topic || "",
              "correlation_data" => scheduled_msg.correlation_data || "",
              "user_properties" => scheduled_msg.user_properties || [],
              # Format-specific payloads - load from scheduled message if available
              "payload_text" => Map.get(scheduled_msg, :payload_text, ""),
              "payload_json" => Map.get(scheduled_msg, :payload_json, ""),
              "payload_hex" => Map.get(scheduled_msg, :payload_hex, ""),
              "payload_file" => Map.get(scheduled_msg, :payload_file, "")
            }
          else
            # New message mode - use default form
            current_form = default_scheduled_message_form()

            # Set pre-selected client ID if provided
            if assigns[:pre_selected_client_id] do
              Map.put(current_form, "client_id", assigns[:pre_selected_client_id])
            else
              current_form
            end
          end

        # Restore uploaded file state if payload_format is "file" and payload_file is not empty
        uploaded_file =
          if form["payload_format"] == "file" && form["payload_file"] != "" do
            restore_uploaded_file_state(form["payload_file"])
          else
            assigns[:uploaded_file]
          end

        # Simplified form handling - no template selection logic needed
        updated_form = form

        socket =
          socket
          |> assign(assigns)
          |> assign(:connected_clients, connected_clients)
          |> assign(:scheduled_message_form, updated_form)
          |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)
          |> assign(:uploaded_file, uploaded_file)

        {:ok, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="modal modal-open">
      <div
        class="modal-box max-w-6xl ml-auto mr-6 mt-6 mb-6 h-[calc(100vh-3rem)] flex flex-col scheduled-message-modal-sidebar"
        id="scheduled-message-modal-content"
      >
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-4 flex-shrink-0">
          <h3 class="text-lg font-semibold flex items-center">
            <.icon name="hero-clock" class="size-5 mr-2" />
            <%= if @edit_mode do %>
              Edit Scheduled Message
            <% else %>
              New Scheduled Message
            <% end %>
          </h3>
          <button
            class="btn btn-sm btn-circle btn-ghost"
            phx-click="close_scheduled_message_modal"
            phx-target={@myself}
          >
            ✕
          </button>
        </div>
        
    <!-- Alert Message -->
        <div :if={@alert_message} class="mb-4 flex-shrink-0">
          <div
            role="alert"
            class={[
              "alert",
              @alert_type == :success && "alert-success",
              @alert_type == :error && "alert-error",
              @alert_type == :warning && "alert-warning"
            ]}
          >
            <svg
              :if={@alert_type == :success}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <svg
              :if={@alert_type == :error}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <svg
              :if={@alert_type == :warning}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <span>{@alert_message}</span>
            <button
              type="button"
              class="btn btn-sm btn-ghost ml-auto"
              phx-click="dismiss_alert"
              phx-target={@myself}
            >
              ✕
            </button>
          </div>
        </div>
        
    <!-- Modal Content with Sidebar Layout -->
        <div
          class="flex-1 overflow-hidden"
          phx-hook="PayloadEditor"
          id={"scheduled-payload-container-#{@myself}"}
        >
          <div class="h-full flex gap-6">
            <!-- Left Column: Main Form (60%) -->
            <div class="flex-1 overflow-y-auto pr-2">
              <.form
                for={%{}}
                as={:scheduled_message}
                phx-submit="save_scheduled_message"
                phx-change="form_changed"
                phx-target={@myself}
                class="space-y-4"
                id="scheduled-message-form"
              >
                <!-- Client ID Selection (disabled for scheduled messages) -->
                <.client_selection
                  form={@scheduled_message_form}
                  connected_clients={@connected_clients}
                  active_broker_name={@active_broker_name || ""}
                  myself={@myself}
                  disabled={true}
                  label="Client (Pre-selected)"
                  required={true}
                />
                
    <!-- Topic and Send Interval Row -->
                <div class="flex items-end gap-4">
                  <!-- Topic Input (flex-1) -->
                  <div class="form-control flex-1">
                    <label class="label">
                      <span class="label-text font-medium">
                        Topic <span class="text-error">*</span>
                      </span>
                    </label>
                    <label class="input input-bordered flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="14538"
                      >
                        <path
                          d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334C323.477333 170.666667 170.666667 323.477333 170.666667 512s152.810667 341.333333 341.333333 341.333333 341.333333-152.810667 341.333333-341.333333S700.522667 170.666667 512 170.666667z m0 128c23.552 0 42.666667 19.114667 42.666667 42.666666v128h128c23.552 0 42.666667 19.114667 42.666667 42.666667s-19.114667 42.666667-42.666667 42.666667h-128v128c0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667v-128h-128c-23.552 0-42.666667-19.114667-42.666667-42.666667s19.114667-42.666667 42.666667-42.666667h128v-128c0-23.552 19.114667-42.666667 42.666667-42.666667z"
                          fill="#172B4D"
                          p-id="14539"
                        >
                        </path>
                      </svg>
                      <input
                        type="text"
                        name="topic"
                        value={@scheduled_message_form["topic"]}
                        placeholder="Topic (e.g., 'sensor/temperature')"
                        class="grow"
                        required
                      />
                    </label>
                  </div>
                  
    <!-- Send Interval (flex-shrink-0) -->
                  <div class="form-control flex-shrink-0">
                    <label class="label">
                      <span class="label-text font-medium">
                        Send Interval <span class="text-error">*</span>
                      </span>
                    </label>
                    <label class="input input-bordered flex items-center gap-2">
                      <input
                        type="number"
                        name="interval_seconds"
                        value={@scheduled_message_form["interval_seconds"] || "5"}
                        min="1"
                        placeholder="Replay Every"
                        class="grow"
                        required
                      />
                      <span class="label-text">second</span>
                    </label>
                  </div>
                </div>
                
    <!-- Unified Payload Editor -->
                <.live_component
                  module={MqttableWeb.UnifiedPayloadEditorComponent}
                  id={"unified-payload-editor-scheduled-#{@myself}"}
                  payload_format={@scheduled_message_form["payload_format"] || "text"}
                  payload_text={@scheduled_message_form["payload_text"] || ""}
                  payload_json={@scheduled_message_form["payload_json"] || ""}
                  payload_hex={@scheduled_message_form["payload_hex"] || ""}
                  payload_file={@scheduled_message_form["payload_file"] || ""}
                  uploaded_file={@uploaded_file}
                  active_broker_name={@active_broker_name}
                />
                
    <!-- QoS, Retain Message, and Submit Button Row (3 equal parts) -->
                <div class="grid grid-cols-3 gap-4 items-center">
                  <!-- QoS Selection (1/3) -->
                  <div class="flex justify-start">
                    <.qos_selection form={@scheduled_message_form} myself={@myself} />
                  </div>
                  
    <!-- Retain Message (1/3) -->
                  <div class="flex justify-center">
                    <.retain_checkbox form={@scheduled_message_form} label="Retain Message" />
                  </div>
                  
    <!-- Submit Button (1/3) -->
                  <div class="flex justify-end">
                    <button type="submit" class="btn btn-primary">
                      <.icon name="hero-clock" class="size-4 mr-2" />
                      <%= if @edit_mode do %>
                        Update Scheduled Message
                      <% else %>
                        Create Scheduled Message
                      <% end %>
                    </button>
                  </div>
                </div>
                
    <!-- MQTT 5.0 Properties Section -->
                <.mqtt5_properties_section
                  form={@scheduled_message_form}
                  myself={@myself}
                  collapsed={@mqtt5_properties_collapsed}
                  show_properties={
                    show_mqtt5_properties?(@scheduled_message_form["client_id"], @active_broker_name)
                  }
                />
              </.form>
            </div>
            
    <!-- Right Column: Template Helper Sidebar (40%) -->
            <div class="w-2/5 border-l border-base-300 pl-6 overflow-y-auto">
              <.live_component
                module={MqttableWeb.TwoTabTemplateHelperComponent}
                id={"two-tab-template-helper-scheduled-#{@myself}"}
                target_textarea_id={"payload-editor-#{@myself}"}
                payload={get_current_payload_for_format(@scheduled_message_form)}
                payload_format={@scheduled_message_form["payload_format"] || "text"}
                active_broker_name={@active_broker_name}
              />
            </div>
          </div>
        </div>
      </div>
      <div class="modal-backdrop" phx-click="close_scheduled_message_modal" phx-target={@myself}>
      </div>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("form_changed", params, socket) do
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.scheduled_message_form, params)
    # Validate payload based on current format
    validated_form = validate_payload_in_form(updated_form)
    {:noreply, assign(socket, :scheduled_message_form, validated_form)}
  end

  @impl true
  def handle_event("client_selection_changed", %{"client_id" => client_id}, socket) do
    # This shouldn't happen since client selection is disabled, but handle it anyway
    updated_form = Map.put(socket.assigns.scheduled_message_form, "client_id", client_id)
    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  # Template-related event handlers removed - using simplified payload editor now

  @impl true
  def handle_event("qos_changed", %{"qos" => qos}, socket) do
    updated_form = Map.put(socket.assigns.scheduled_message_form, "qos", qos)
    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    current_state = socket.assigns.mqtt5_properties_collapsed
    new_state = !current_state

    # Save the new state to ui_state
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      updated_mqtt5_states = Map.put(mqtt5_collapsed_states, broker_name, new_state)
      updated_ui_state = Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_states)
      Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_state)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []
    new_property = %{"key" => "", "value" => ""}
    updated_properties = current_properties ++ [new_property]

    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []
    updated_properties = List.delete_at(current_properties, index)

    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("save_scheduled_message", params, socket) do
    # Extract form parameters and trim topic whitespace
    client_id = params["client_id"]
    topic = String.trim(params["topic"] || "")
    # Extract payload format from the correct field (either payload_format or format-scheduled-X)
    payload_format = extract_payload_format(params, socket.assigns.scheduled_message_form)
    payload = get_current_payload_for_format_from_params(params, payload_format)
    qos = parse_integer_param(params["qos"], 0)
    retain = params["retain"] == "on"

    # Calculate interval in milliseconds from seconds
    interval_seconds = parse_integer_param(params["interval_seconds"], 5)
    interval_ms = interval_seconds * 1000

    # Process payload and validate AFTER template evaluation
    {final_payload, payload_error} = process_payload(payload, socket.assigns[:active_broker_name])

    validated_form =
      validate_payload_in_form_after_template(
        %{"payload_format" => payload_format},
        final_payload,
        payload_format
      )

    # Validate required fields and payload format
    if client_id != "" && topic != "" && interval_ms > 0 &&
         validated_form["payload_validation_error"] == nil && payload_error == nil do
      # Create scheduled message data with MQTT 5 properties and template info
      scheduled_message = %{
        topic: topic,
        payload_format: payload_format,
        file_encoding: params["file_encoding"] || "binary",
        qos: qos,
        retain: retain,
        interval_ms: interval_ms,
        # MQTT 5.0 properties (trim response_topic whitespace)
        content_type: params["content_type"] || "",
        payload_format_indicator: params["payload_format_indicator"] == "on",
        message_expiry_interval: parse_integer_param(params["message_expiry_interval"], 0),
        topic_alias: parse_integer_param(params["topic_alias"], 0),
        response_topic: String.trim(params["response_topic"] || ""),
        correlation_data: params["correlation_data"] || "",
        user_properties: extract_user_properties_from_params(params),
        # Format-specific payloads
        payload_text: params["payload_text"] || "",
        payload_json: params["payload_json"] || "",
        payload_hex: params["payload_hex"] || "",
        payload_file: params["payload_file"] || ""
      }

      # Send message to parent to save scheduled message first
      if socket.assigns.edit_mode do
        send(
          self(),
          {:update_scheduled_message, client_id, socket.assigns.edit_index, scheduled_message}
        )
      else
        send(self(), {:add_scheduled_message, client_id, scheduled_message})
      end

      Process.send(self(), {:close_scheduled_message_modal}, [])

      {:noreply, socket}
    else
      # Validation failed, show error message
      error_message =
        cond do
          client_id == "" ->
            "Please select a client"

          topic == "" ->
            "Please enter a topic"

          interval_ms <= 0 ->
            "Please enter a valid interval"

          payload_error != nil ->
            "Template error: #{payload_error}"

          validated_form["payload_validation_error"] != nil ->
            "Payload validation failed: #{validated_form["payload_validation_error"]}"

          true ->
            "Please fill in all required fields"
        end

      timestamped_message = add_timestamp_to_message(error_message)

      socket =
        socket
        |> assign(:alert_message, timestamped_message)
        |> assign(:alert_type, :error)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_scheduled_message_modal", _params, socket) do
    # Send message to parent to close modal
    send(self(), {:close_scheduled_message_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("dismiss_alert", _params, socket) do
    socket =
      socket
      |> assign(:alert_message, nil)
      |> assign(:alert_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:scheduled_message_form, fn form ->
        form
        |> Map.put("payload_format", "text")
        |> Map.put("payload_text", "")
        |> Map.put("payload_json", "")
        |> Map.put("payload_hex", "")
        |> Map.put("payload_file", "")
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:scheduled_message_form, fn form ->
        form
        |> Map.put("payload_format", format)
        |> Map.put(
          "file_encoding",
          if(format == "file", do: form["file_encoding"] || "binary", else: nil)
        )
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("file_encoding_changed", %{"encoding" => encoding}, socket) do
    socket =
      socket
      |> update(:scheduled_message_form, fn form ->
        Map.put(form, "file_encoding", encoding)
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("validate", _params, socket) do
    # Just validate the form, don't process files yet
    {:noreply, socket}
  end

  # File upload is now handled directly in JavaScript, no LiveView events needed

  # Handle info messages

  def handle_info({:payload_editor_changed, payload, payload_format}, socket) do
    # Update the scheduled message form with the new payload and format
    # Only use format-specific payload fields, no main "payload" field
    updated_form =
      socket.assigns.scheduled_message_form
      |> Map.put("payload_format", payload_format)
      |> update_format_specific_payload_in_form(payload_format, payload)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  def handle_info({:file_uploaded, file_info}, socket) do
    # Handle file upload from UnifiedPayloadEditorComponent
    {:ok, updated_socket} = handle_file_upload_update(%{file_uploaded: file_info}, socket)
    # After successful file upload, notify the payload editor component about the file
    # This ensures the payload_file field is updated for form submission
    stored_filename = updated_socket.assigns.scheduled_message_form["payload_file"]
    send(self(), {:payload_editor_changed, stored_filename, "file"})
    {:noreply, updated_socket}
  end

  # Helper functions

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  # Helper function to normalize scheduled message data (convert string keys to atom keys)
  defp normalize_scheduled_message(scheduled_msg) when is_map(scheduled_msg) do
    # Convert string keys to atom keys if needed
    if Map.has_key?(scheduled_msg, "topic") do
      # Has string keys, convert to atom keys
      for {key, val} <- scheduled_msg, into: %{}, do: {String.to_atom(key), val}
    else
      # Already has atom keys or is empty
      scheduled_msg
    end
  end

  defp normalize_scheduled_message(scheduled_msg), do: scheduled_msg

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(to_string(param_key)) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp parse_integer_param(value, default) when is_binary(value) do
    case Integer.parse(value) do
      {int_val, ""} -> int_val
      _ -> default
    end
  end

  defp parse_integer_param(value, _default) when is_integer(value), do: value
  defp parse_integer_param(_, default), do: default

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id && client_id != "" && active_broker_name do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp get_connected_clients(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get broker-specific client IDs
    broker_client_ids = get_broker_client_ids(broker_name)

    # Get all connected clients
    all_connected_clients = Mqttable.MqttClient.Manager.get_connected_clients()

    # Filter to only include clients that belong to this broker
    all_connected_clients
    |> Enum.filter(fn client -> client.client_id in broker_client_ids end)
  end

  defp get_connected_clients(_broker_name) do
    # If no broker name provided, return empty list
    []
  end

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  defp default_scheduled_message_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload_format" => "text",
      "payload_validation_error" => nil,
      "qos" => "0",
      "retain" => false,
      "interval_seconds" => "5",
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => [],
      # Format-specific payloads
      "payload_text" => "",
      "payload_json" => "",
      "payload_hex" => "",
      "payload_file" => ""
    }
  end

  # Get the current payload based on the selected format
  defp get_current_payload_for_format(form) do
    format = Map.get(form, "payload_format", "text")

    case format do
      "text" -> Map.get(form, "payload_text", "")
      "json" -> Map.get(form, "payload_json", "")
      "hex" -> Map.get(form, "payload_hex", "")
      "file" -> Map.get(form, "payload_file", "")
      _ -> Map.get(form, "payload_text", "")
    end
  end

  # Get the current payload from params based on the selected format
  defp get_current_payload_for_format_from_params(params, format) do
    case format do
      "text" -> Map.get(params, "payload_text", "")
      "json" -> Map.get(params, "payload_json", "")
      "hex" -> Map.get(params, "payload_hex", "")
      "file" -> Map.get(params, "payload_file", "")
      _ -> Map.get(params, "payload_text", "")
    end
  end

  defp update_form_with_params(current_form, params) do
    # Handle both nested (scheduled_message) and direct parameters
    form_params = params["scheduled_message"] || params

    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(form_params, current_form, fn {key, value}, acc ->
        case key do
          "qos" ->
            # Keep QoS as string for UI consistency
            Map.put(acc, key, value)

          "retain" ->
            Map.put(acc, key, value == "on")

          "payload_format_indicator" ->
            Map.put(acc, key, value == "on")

          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 -> Map.put(acc, key, int_val)
                  _ -> Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          # Skip internal Phoenix form fields
          key when key in ["_target", "_csrf_token"] ->
            acc

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle checkboxes that don't send values when unchecked
    final_form =
      updated_form
      |> Map.put("retain", Map.get(form_params, "retain") == "on")
      |> Map.put(
        "payload_format_indicator",
        Map.get(form_params, "payload_format_indicator") == "on"
      )

    # Handle user properties
    user_properties = extract_user_properties_from_params(form_params)

    if length(user_properties) > 0 do
      Map.put(final_form, "user_properties", user_properties)
    else
      final_form
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key_str = to_string(key)

      key_str
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        param_key_str = to_string(param_key)

        if String.contains?(param_key_str, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
    |> Enum.filter(fn %{"key" => key, "value" => value} ->
      key != "" && value != ""
    end)
  end

  # Payload validation functions (copied from send_message_modal_component.ex)

  defp validate_payload_in_form(form) do
    format = Map.get(form, "payload_format", "text")
    payload = get_current_payload_for_format(form)

    case validate_payload(payload, format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> {:ok, payload}
      _ -> {:ok, payload}
    end
  end

  defp validate_file_payload(""),
    do: {:error, "Please upload a file or select a different format"}

  defp validate_file_payload(file_path) when is_binary(file_path) do
    # Check if the file exists in storage
    if Mqttable.Uploads.FileStorage.file_exists?(file_path) do
      {:ok, file_path}
    else
      {:error, "File not found. Please upload the file again."}
    end
  end

  defp validate_payload_in_form_after_template(form, final_payload, payload_format) do
    case validate_payload(final_payload, payload_format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp process_payload(payload, broker_name) do
    # Check if payload contains template syntax and render if needed
    if has_template_syntax?(payload) do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(broker_name)

      case Mqttable.Templating.Engine.render(payload, %{}, variables) do
        {:ok, rendered_payload} ->
          {rendered_payload, nil}

        {:error, reason} ->
          {payload, "Template error: #{reason}"}
      end
    else
      # Use payload as-is for plain text
      {payload, nil}
    end
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end

  defp extract_payload_format(params, form) do
    # First try to get from payload_format field (from component state)
    case Map.get(params, "payload_format") do
      nil ->
        # If not found, try to extract from format-scheduled-X field (from radio buttons)
        format_key = Enum.find(Map.keys(params), &String.starts_with?(&1, "format-scheduled-"))

        case format_key do
          nil ->
            # Fall back to form state or default
            Map.get(form, "payload_format", "text")

          key ->
            Map.get(params, key, "text")
        end

      format ->
        format
    end
  end

  defp validate_json_payload(""), do: {:ok, ""}

  defp validate_json_payload(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> {:ok, payload}
      {:error, _} -> {:error, "Invalid JSON format"}
    end
  end

  defp validate_hex_payload(""), do: {:ok, ""}

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        {:ok, payload}

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        {:error, "Invalid hex format. Use only 0-9, A-F characters"}

      rem(String.length(cleaned), 2) != 0 ->
        {:error, "Hex payload must have even number of characters"}

      true ->
        {:ok, payload}
    end
  end

  # Add RFC3339 timestamp to alert message
  defp add_timestamp_to_message(message) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    "[#{timestamp}] #{message}"
  end

  # Live Preview Section
  attr :payload, :string, required: true
  attr :active_broker_name, :string, required: true

  def live_preview_section(assigns) do
    preview_result = generate_live_preview(assigns.payload, assigns.active_broker_name)
    assigns = assign(assigns, :preview_result, preview_result)

    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">🔍 Live Preview</span>
      </label>
      <div class="bg-base-100 border border-base-300 rounded-lg p-3 h-64 overflow-y-auto">
        <%= if String.trim(@payload) == "" do %>
          <div class="flex flex-col items-center justify-center h-full text-base-content/40">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-12 mb-2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
              />
            </svg>
            <p class="text-sm font-medium">File upload preview</p>
            <p class="text-xs">Upload a file to see preview</p>
          </div>
        <% else %>
          <%= case @preview_result do %>
            <% {:ok, result} -> %>
              <pre class="whitespace-pre-wrap text-success text-sm font-mono"><%= result %></pre>
            <% {:error, error} -> %>
              <pre class="whitespace-pre-wrap text-error text-sm font-mono"><%= error %></pre>
          <% end %>
        <% end %>
      </div>
    </div>
    """
  end

  defp generate_live_preview(payload, active_broker_name) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(active_broker_name)

      case Mqttable.Templating.Engine.render(payload, %{}, variables) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, inspect(error)}
      end
    else
      {:ok, payload}
    end
  end

  # Helper function to update format-specific payload in form state
  defp update_format_specific_payload_in_form(form_state, format, payload) do
    case format do
      "text" -> Map.put(form_state, "payload_text", payload)
      "json" -> Map.put(form_state, "payload_json", payload)
      "hex" -> Map.put(form_state, "payload_hex", payload)
      "file" -> Map.put(form_state, "payload_file", payload)
      _ -> Map.put(form_state, "payload_text", payload)
    end
  end

  # Handle file upload success update
  defp handle_file_upload_update(assigns, socket) do
    file_info = assigns[:file_uploaded]

    Logger.info(
      "ScheduledMessageModalComponent: File upload update - #{file_info.filename} stored as #{file_info.stored_filename}"
    )

    # Update form state with file information
    updated_form =
      socket.assigns[:scheduled_message_form]
      |> Map.put("payload_file", file_info.stored_filename)
      |> Map.put("payload_format", "file")
      |> Map.put("payload_validation_error", nil)

    socket =
      socket
      |> assign(:scheduled_message_form, updated_form)
      |> assign(:uploaded_file, %{
        filename: file_info.filename,
        stored_filename: file_info.stored_filename,
        size: file_info.size,
        type: file_info.type
      })

    {:ok, socket}
  end

  # Handle file upload error update
  defp handle_file_upload_error_update(assigns, socket) do
    error_message = assigns[:file_upload_error]

    Logger.error("ScheduledMessageModalComponent: File upload error - #{error_message}")

    # Update form state with error
    updated_form =
      socket.assigns[:scheduled_message_form]
      |> Map.put("payload_validation_error", error_message)
      |> Map.put("payload_file", "")

    socket =
      socket
      |> assign(:scheduled_message_form, updated_form)
      |> assign(:uploaded_file, nil)

    {:ok, socket}
  end

  # Restore uploaded file state from stored filename
  defp restore_uploaded_file_state(stored_filename)
       when is_binary(stored_filename) and stored_filename != "" do
    # Check if the file still exists in storage
    if Mqttable.Uploads.FileStorage.file_exists?(stored_filename) do
      # Try to get file info from storage
      case Mqttable.Uploads.FileStorage.get_file_info(stored_filename) do
        {:ok, file_info} ->
          # Extract original filename from stored filename (remove timestamp suffix)
          original_filename = extract_original_filename(stored_filename)

          %{
            filename: original_filename,
            stored_filename: stored_filename,
            size: file_info.size || 0,
            # File type is not stored in FileStorage, so we can't restore it
            type: ""
          }

        {:error, _reason} ->
          # File exists but can't get info, create minimal state
          original_filename = extract_original_filename(stored_filename)

          %{
            filename: original_filename,
            stored_filename: stored_filename,
            size: 0,
            type: ""
          }
      end
    else
      # File doesn't exist anymore, return nil
      nil
    end
  end

  defp restore_uploaded_file_state(_), do: nil

  # Extract original filename from stored filename by removing timestamp suffix
  defp extract_original_filename(stored_filename) when is_binary(stored_filename) do
    # Stored filename format: "basename_timestamp.extension"
    # We need to remove the "_timestamp" part
    case String.split(stored_filename, "_") do
      [_base_name] ->
        # No underscore found, return as-is
        stored_filename

      parts when length(parts) >= 2 ->
        # Get all parts except the last one (which contains timestamp)
        base_parts = Enum.drop(parts, -1)
        last_part = List.last(parts)

        # Check if the last part looks like a timestamp with extension
        case String.split(last_part, ".") do
          [timestamp_str | extension_parts] ->
            # Try to parse the timestamp part as integer
            case Integer.parse(timestamp_str) do
              {_timestamp, ""} ->
                # It's a valid timestamp, reconstruct original filename
                base_name = Enum.join(base_parts, "_")

                if length(extension_parts) > 0 do
                  extension = Enum.join(extension_parts, ".")
                  "#{base_name}.#{extension}"
                else
                  base_name
                end

              _ ->
                # Not a timestamp, return as-is
                stored_filename
            end

          _ ->
            # No extension, return as-is
            stored_filename
        end

      _ ->
        # Fallback, return as-is
        stored_filename
    end
  end

  defp extract_original_filename(_), do: ""
end
